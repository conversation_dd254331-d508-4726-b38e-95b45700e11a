# Rovella Backend API

基于 NestJS 构建的现代化后端 API 服务，提供完整的用户管理、权限控制、文件上传等功能。

## 🚀 技术栈

- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **数据库**: MySQL 8.0 + TypeORM
- **缓存**: Redis (可选)
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **文件存储**: 本地存储 / 腾讯云 COS
- **日志**: Winston
- **测试**: Jest

## 📁 项目结构

```
src/
├── common/                 # 公共模块
│   ├── decorators/        # 装饰器
│   ├── filters/           # 异常过滤器
│   ├── guards/            # 守卫
│   ├── interceptors/      # 拦截器
│   └── pipes/             # 管道
├── config/                # 配置文件
│   ├── dev.yml           # 开发环境配置
│   ├── test.yml          # 测试环境配置
│   ├── prod.yml          # 生产环境配置
│   └── index.ts          # 配置加载器
├── module/               # 业务模块
│   ├── common/           # 通用模块 (Redis, 文件上传等)
│   ├── main/             # 主模块 (首页, 登录等)
│   ├── monitor/          # 监控模块
│   ├── system/           # 系统管理模块
│   └── upload/           # 文件上传模块
├── app.module.ts         # 应用根模块
└── main.ts              # 应用入口
```

## 🛠️ 开发环境设置

### 1. 环境要求

- Node.js >= 16.0.0
- pnpm >= 8.0.0
- MySQL >= 8.0
- Redis >= 6.0 (可选)

### 2. 安装依赖

```bash
pnpm install
```

### 3. 数据库配置

创建数据库：
```sql
CREATE DATABASE `nest-admin` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

导入初始数据：
```bash
mysql -u root -p nest-admin < db/init.sql
```

### 4. 配置文件

修改 `src/config/dev.yml` 中的配置：

```yaml
# 数据库配置
db:
  mysql:
    host: '127.0.0.1'
    username: 'root'
    password: 'your_password'
    database: 'nest-admin'
    port: 3306

# Redis 配置 (可选)
redis:
  host: 'localhost'
  password: ''
  port: 6379
  db: 2

# JWT 配置
jwt:
  secretkey: 'your_secret_key'
  expiresin: '1h'
```

### 5. 启动服务

```bash
# 开发模式
pnpm run dev

# 生产模式
pnpm run build
pnpm run start:prod
```

## 📚 API 文档

启动服务后访问: http://localhost:8080/swagger-ui

## 🔐 权限系统

系统采用 RBAC (基于角色的访问控制) 模型：

- **用户 (User)**: 系统使用者
- **角色 (Role)**: 权限的集合
- **权限 (Permission)**: 具体的操作权限
- **菜单 (Menu)**: 前端菜单和路由

### 权限装饰器

```typescript
// 需要特定权限
@RequirePermissions('system:user:list')
@Get()
findAll() {
  // ...
}

// 需要特定角色
@RequireRoles('admin')
@Post()
create() {
  // ...
}
```

## 📁 文件上传

支持本地存储和腾讯云 COS：

```typescript
@Post('upload')
@UseInterceptors(FileInterceptor('file'))
uploadFile(@UploadedFile() file: Express.Multer.File) {
  // 文件上传逻辑
}
```

配置文件存储方式：
```yaml
app:
  file:
    isLocal: true  # true: 本地存储, false: 云存储
    location: '../upload'
    domain: 'http://localhost:8080'
```

## 🔍 日志系统

使用 Winston 进行日志管理：

```typescript
import { Logger } from '@nestjs/common';

export class SomeService {
  private readonly logger = new Logger(SomeService.name);

  someMethod() {
    this.logger.log('This is a log message');
    this.logger.error('This is an error message');
    this.logger.warn('This is a warning message');
  }
}
```

## 🧪 测试

```bash
# 单元测试
pnpm run test

# e2e 测试
pnpm run test:e2e

# 测试覆盖率
pnpm run test:cov
```

## 🚀 部署

### Vercel 部署

1. 配置环境变量：
   - `NODE_ENV=production`
   - `DB_HOST` - 数据库主机
   - `DB_USERNAME` - 数据库用户名
   - `DB_PASSWORD` - 数据库密码
   - `DB_DATABASE` - 数据库名称
   - `JWT_SECRET` - JWT 密钥

2. 确保 `vercel.json` 配置正确

3. 部署到 Vercel

### Docker 部署

```bash
# 构建镜像
docker build -t rovella-backend .

# 运行容器
docker run -p 8080:8080 rovella-backend
```

## 🔧 常见问题

### 1. 数据库连接失败

检查数据库配置和网络连接：
```yaml
db:
  mysql:
    host: 'localhost'  # 确保主机地址正确
    port: 3306         # 确保端口正确
    username: 'root'   # 确保用户名正确
    password: 'password' # 确保密码正确
```

### 2. Redis 连接失败

如果不使用 Redis，可以在相关模块中注释掉 Redis 相关代码。

### 3. 文件上传失败

检查文件上传目录权限和配置：
```yaml
app:
  file:
    location: '../upload'  # 确保目录存在且可写
```

## 📝 开发规范

### 代码风格

项目使用 ESLint + Prettier 进行代码格式化：

```bash
# 检查代码风格
pnpm run lint

# 自动修复
pnpm run lint:fix

# 格式化代码
pnpm run format
```

### 提交规范

使用 Conventional Commits 规范：

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。
