import * as React from "react"

export const Simplify2Icon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          d="M4 20C4 20.5523 3.55228 21 3 21C2.44772 21 2 20.5523 2 20V6C2 4.34315 3.34315 3 5 3C6.65685 3 8 4.34315 8 6V15C8 15.5523 8.44772 16 9 16C9.55228 16 10 15.5523 10 15V9.5C10 7.84315 11.3431 6.5 13 6.5C14.6569 6.5 16 7.84315 16 9.5V10C16 10.5523 16.4477 11 17 11H19.5858L18.2929 9.70711C17.9024 9.31658 17.9024 8.68342 18.2929 8.29289C18.6834 7.90237 19.3166 7.90237 19.7071 8.29289L22.7071 11.2929C23.0976 11.6834 23.0976 12.3166 22.7071 12.7071L19.7071 15.7071C19.3166 16.0976 18.6834 16.0976 18.2929 15.7071C17.9024 15.3166 17.9024 14.6834 18.2929 14.2929L19.5858 13H17C15.3431 13 14 11.6569 14 10V9.5C14 8.94772 13.5523 8.5 13 8.5C12.4477 8.5 12 8.94771 12 9.5V15C12 16.6569 10.6569 18 9 18C7.34315 18 6 16.6569 6 15V6C6 5.44772 5.55228 5 5 5C4.44772 5 4 5.44772 4 6V20Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

Simplify2Icon.displayName = "Simplify2Icon"
