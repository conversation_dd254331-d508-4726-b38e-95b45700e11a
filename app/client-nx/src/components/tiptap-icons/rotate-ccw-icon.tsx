import * as React from "react"

export const RotateCcwIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          d="M12 2L11.9962 2.00001C9.22213 2.01045 6.55946 3.0929 4.56496 5.02104L4.55289 5.0329L4 5.58579V3C4 2.44772 3.55228 2 3 2C2.44772 2 2 2.44772 2 3V8C2 8.55228 2.44772 9 3 9H8C8.55228 9 9 8.55228 9 8C9 7.44772 8.55228 7 8 7H5.41422L5.96097 6.45325C7.58329 4.88786 9.7473 4.00897 12.0019 4.00001C13.5835 4.00038 15.1295 4.46955 16.4446 5.34825C17.7602 6.2273 18.7855 7.47673 19.391 8.93854C19.9965 10.4003 20.155 12.0089 19.8463 13.5607C19.5376 15.1126 18.7757 16.538 17.6569 17.6569C16.538 18.7757 15.1126 19.5376 13.5607 19.8463C12.0089 20.155 10.4003 19.9965 8.93853 19.391C7.47672 18.7855 6.22729 17.7602 5.34824 16.4446C4.46919 15.129 4 13.5823 4 12C4 11.4477 3.55228 11 3 11C2.44772 11 2 11.4477 2 12C2 13.9778 2.58649 15.9112 3.6853 17.5557C4.78412 19.2002 6.3459 20.4819 8.17317 21.2388C10.0004 21.9957 12.0111 22.1937 13.9509 21.8079C15.8907 21.422 17.6725 20.4696 19.0711 19.0711C20.4696 17.6726 21.422 15.8907 21.8079 13.9509C22.1937 12.0111 21.9957 10.0004 21.2388 8.17317C20.4819 6.34591 19.2002 4.78412 17.5557 3.68531C15.9112 2.5865 13.9778 2.00001 12 2Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

RotateCcwIcon.displayName = "RotateCcwIcon"
