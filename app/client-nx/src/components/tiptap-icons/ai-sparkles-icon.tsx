import * as React from "react"

export const AiSparklesIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.94924 1.68544C6.81364 1.27624 6.43108 1 6 1C5.56892 1 5.18636 1.27624 5.05076 1.68544L4.3711 3.73642C4.2717 4.03638 4.03638 4.2717 3.73642 4.3711L1.68544 5.05076C1.27624 5.18636 1 5.56892 1 6C1 6.43108 1.27624 6.81364 1.68544 6.94924L3.73642 7.6289C4.03638 7.7283 4.2717 7.96362 4.3711 8.26358L5.05076 10.3146C5.18636 10.7238 5.56892 11 6 11C6.43108 11 6.81364 10.7238 6.94924 10.3146L7.6289 8.26358C7.7283 7.96362 7.96362 7.7283 8.26358 7.6289L10.3146 6.94924C10.7238 6.81364 11 6.43108 11 6C11 5.56892 10.7238 5.18636 10.3146 5.05076L8.26358 4.3711C7.96362 4.2717 7.7283 4.03638 7.6289 3.73642L6.94924 1.68544ZM4.95595 6C5.38537 5.74447 5.74447 5.38537 6 4.95595C6.25554 5.38537 6.61463 5.74447 7.04405 6C6.61463 6.25554 6.25554 6.61463 6 7.04405C5.74447 6.61463 5.38537 6.25554 4.95595 6Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.9492 5.68544C14.8136 5.27624 14.4311 5 14 5C13.5689 5 13.1864 5.27624 13.0508 5.68544L11.3755 10.7408C11.2761 11.0408 11.0408 11.2761 10.7408 11.3755L5.68544 13.0508C5.27624 13.1864 5 13.5689 5 14C5 14.4311 5.27624 14.8136 5.68544 14.9492L10.7408 16.6245C11.0408 16.7239 11.2761 16.9592 11.3755 17.2592L13.0508 22.3146C13.1864 22.7238 13.5689 23 14 23C14.4311 23 14.8136 22.7238 14.9492 22.3146L16.6245 17.2592C16.7239 16.9592 16.9592 16.7239 17.2592 16.6245L22.3146 14.9492C22.7238 14.8136 23 14.4311 23 14C23 13.5689 22.7238 13.1864 22.3146 13.0508L17.2592 11.3755C16.9592 11.2761 16.7239 11.0408 16.6245 10.7408L14.9492 5.68544ZM13.274 11.3699L14 9.17903L14.726 11.3699C15.0242 12.2698 15.7302 12.9758 16.6301 13.274L18.821 14L16.6301 14.726C15.7302 15.0242 15.0242 15.7302 14.726 16.6301L14 18.821L13.274 16.6301C12.9758 15.7302 12.2698 15.0242 11.3699 14.726L9.17903 14L11.3699 13.274C12.2698 12.9758 12.9758 12.2698 13.274 11.3699Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

AiSparklesIcon.displayName = "AiSparklesIcon"
