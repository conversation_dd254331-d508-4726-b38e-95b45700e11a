import * as React from "react"

export const RefreshAiIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          d="M11.9949 2.00001C9.22128 2.01079 6.55914 3.0932 4.56496 5.02103L4.55289 5.03289L4 5.58579V3C4 2.44771 3.55228 2 3 2C2.44771 2 2 2.44771 2 3V7.99931L2 8.003C2.0004 8.1375 2.02735 8.26575 2.07588 8.38278C2.12357 8.49805 2.19374 8.6062 2.2864 8.70055L2.29945 8.7136C2.3938 8.80626 2.50195 8.87643 2.61722 8.92412C2.73512 8.97301 2.86441 9 3 9H8C8.55228 9 9 8.55228 9 8C9 7.44772 8.55228 7 8 7H5.41421L5.96097 6.45325C7.58334 4.8878 9.74744 4.00891 12.0021 4C13.7624 4.00045 15.4735 4.58152 16.8701 5.65317C18.2673 6.72525 19.2716 8.22838 19.7274 9.92945C19.8704 10.4629 20.4187 10.7795 20.9522 10.6366C21.4856 10.4936 21.8022 9.94528 21.6593 9.41181C21.0895 7.28548 19.8341 5.40656 18.0876 4.06647C16.3412 2.72637 14.2014 2 12 2L11.9962 2.00001L11.9949 2.00001Z"
          fill="currentColor"
        />
        <path
          d="M10.6366 20.9522C10.4936 21.4856 9.9453 21.8022 9.41183 21.6593C7.2855 21.0895 5.40658 19.8341 4.06649 18.0876C2.72639 16.3412 2.00002 14.2013 2.00002 12C2.00002 11.4477 2.44774 11 3.00002 11C3.55231 11 4.00002 11.4477 4.00002 12C4.00002 13.7611 4.58112 15.4729 5.65319 16.8701C6.72527 18.2672 8.2284 19.2716 9.92947 19.7274C10.4629 19.8703 10.7795 20.4187 10.6366 20.9522Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.9379 11.653C17.7926 11.2605 17.4184 11 17 11C16.5816 11 16.2074 11.2605 16.0621 11.653L14.9511 14.6556C14.9004 14.7925 14.7925 14.9004 14.6556 14.9511L11.653 16.0621C11.2605 16.2074 11 16.5816 11 17C11 17.4184 11.2605 17.7926 11.653 17.9379L14.6556 19.0489C14.7925 19.0996 14.9004 19.2075 14.9511 19.3444L16.0621 22.347C16.2074 22.7395 16.5816 23 17 23C17.4184 23 17.7926 22.7395 17.9379 22.347L19.0489 19.3444C19.0996 19.2075 19.2075 19.0996 19.3444 19.0489L22.347 17.9379C22.7395 17.7926 23 17.4184 23 17C23 16.5816 22.7395 16.2074 22.347 16.0621L19.3444 14.9511C19.2075 14.9004 19.0996 14.7925 19.0489 14.6556L17.9379 11.653ZM16.8268 15.3497L17 14.8815L17.1732 15.3497C17.4265 16.034 17.966 16.5735 18.6503 16.8268L19.1185 17L18.6503 17.1732C17.966 17.4265 17.4265 17.966 17.1732 18.6503L17 19.1185L16.8268 18.6503C16.5735 17.966 16.034 17.4265 15.3497 17.1732L14.8815 17L15.3497 16.8268C16.034 16.5735 16.5735 16.034 16.8268 15.3497Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

RefreshAiIcon.displayName = "RefreshAiIcon"
