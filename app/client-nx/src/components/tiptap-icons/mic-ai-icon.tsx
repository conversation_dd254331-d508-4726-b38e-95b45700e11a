import * as React from "react"

export const MicAiIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          d="M10.5 1C9.43913 1 8.42172 1.42143 7.67157 2.17157C6.92143 2.92172 6.5 3.93913 6.5 5V12C6.5 13.0609 6.92143 14.0783 7.67157 14.8284C8.42172 15.5786 9.43913 16 10.5 16C11.5609 16 12.5783 15.5786 13.3284 14.8284C14.0786 14.0783 14.5 13.0609 14.5 12C14.5 11.4477 14.0523 11 13.5 11C12.9477 11 12.5 11.4477 12.5 12C12.5 12.5304 12.2893 13.0391 11.9142 13.4142C11.5391 13.7893 11.0304 14 10.5 14C9.96957 14 9.46086 13.7893 9.08579 13.4142C8.71071 13.0391 8.5 12.5304 8.5 12V5C8.5 4.46957 8.71071 3.96086 9.08579 3.58579C9.46086 3.21071 9.96957 3 10.5 3C11.0523 3 11.5 2.55228 11.5 2C11.5 1.44772 11.0523 1 10.5 1Z"
          fill="currentColor"
        />
        <path
          d="M4.5 10C4.5 9.44771 4.05228 9 3.5 9C2.94772 9 2.5 9.44771 2.5 10V12C2.5 13.7611 3.0811 15.4729 4.15318 16.8701C5.22525 18.2672 6.72838 19.2716 8.42945 19.7274C8.7827 19.8221 9.14038 19.892 9.5 19.9373V22C9.5 22.5523 9.94772 23 10.5 23C11.0523 23 11.5 22.5523 11.5 22V19.9373C12.2039 19.8486 12.8974 19.6661 13.5615 19.391C15.1885 18.7171 16.5477 17.5251 17.4282 16C17.7043 15.5217 17.5405 14.9101 17.0622 14.634C16.5839 14.3578 15.9723 14.5217 15.6962 15C15.0358 16.1438 14.0164 17.0378 12.7961 17.5433C11.5758 18.0487 10.2229 18.1374 8.94709 17.7956C7.67129 17.4537 6.54394 16.7004 5.73988 15.6526C4.93583 14.6047 4.5 13.3208 4.5 12L4.5 11.9977V10Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.4492 1.68544C17.3136 1.27624 16.9311 1 16.5 1C16.0689 1 15.6864 1.27624 15.5508 1.68544L14.8711 3.73642C14.7717 4.03638 14.5364 4.2717 14.2364 4.3711L12.1854 5.05076C11.7762 5.18636 11.5 5.56892 11.5 6C11.5 6.43108 11.7762 6.81364 12.1854 6.94924L14.2364 7.6289C14.5364 7.7283 14.7717 7.96362 14.8711 8.26358L15.5508 10.3146C15.6864 10.7238 16.0689 11 16.5 11C16.9311 11 17.3136 10.7238 17.4492 10.3146L18.1289 8.26358C18.2283 7.96362 18.4636 7.7283 18.7636 7.6289L20.8146 6.94924C21.2238 6.81364 21.5 6.43108 21.5 6C21.5 5.56892 21.2238 5.18636 20.8146 5.05076L18.7636 4.3711C18.4636 4.2717 18.2283 4.03638 18.1289 3.73642L17.4492 1.68544ZM15.4559 6C15.8854 5.74447 16.2445 5.38537 16.5 4.95595C16.7555 5.38537 17.1146 5.74447 17.5441 6C17.1146 6.25554 16.7555 6.61463 16.5 7.04405C16.2445 6.61463 15.8854 6.25554 15.4559 6Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

MicAiIcon.displayName = "MicAiIcon"
