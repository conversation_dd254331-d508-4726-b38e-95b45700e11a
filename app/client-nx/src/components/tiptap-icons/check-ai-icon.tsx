import * as React from "react"

export const CheckAiIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7 2C7.43108 2 7.81364 2.27624 7.94924 2.68544L8.6289 4.73642C8.7283 5.03638 8.96362 5.2717 9.26358 5.3711L11.3146 6.05076C11.7238 6.18636 12 6.56892 12 7C12 7.43108 11.7238 7.81364 11.3146 7.94924L9.26358 8.6289C8.96362 8.7283 8.7283 8.96362 8.6289 9.26358L7.94924 11.3146C7.81364 11.7238 7.43108 12 7 12C6.56892 12 6.18636 11.7238 6.05076 11.3146L5.3711 9.26358C5.2717 8.96362 5.03638 8.7283 4.73642 8.6289L2.68544 7.94924C2.27624 7.81364 2 7.43108 2 7C2 6.56892 2.27624 6.18636 2.68544 6.05076L4.73642 5.3711C5.03638 5.2717 5.2717 5.03638 5.3711 4.73642L6.05076 2.68544C6.18636 2.27624 6.56892 2 7 2ZM7 5.95595C6.74447 6.38537 6.38537 6.74447 5.95595 7C6.38537 7.25554 6.74447 7.61463 7 8.04405C7.25554 7.61463 7.61463 7.25554 8.04405 7C7.61463 6.74447 7.25554 6.38537 7 5.95595Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M21.7071 8.29289C22.0976 8.68342 22.0976 9.31658 21.7071 9.70711L10.7071 20.7071C10.3166 21.0976 9.68342 21.0976 9.29289 20.7071L4.29289 15.7071C3.90237 15.3166 3.90237 14.6834 4.29289 14.2929C4.68342 13.9024 5.31658 13.9024 5.70711 14.2929L10 18.5858L20.2929 8.29289C20.6834 7.90237 21.3166 7.90237 21.7071 8.29289Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

CheckAiIcon.displayName = "CheckAiIcon"
