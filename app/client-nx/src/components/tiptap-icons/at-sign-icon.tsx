import * as React from "react"

export const AtSignIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.5273 1.29427C12.1156 0.724951 9.58234 0.986597 7.33794 2.03682C5.09354 3.08704 3.26948 4.8643 2.1613 7.08066C1.05312 9.29702 0.725742 11.8226 1.23221 14.2483C1.73868 16.6739 3.04931 18.8575 4.95178 20.4453C6.85424 22.033 9.23706 22.9319 11.7142 22.9963C14.1913 23.0607 16.6176 22.2868 18.6 20.8C19.0418 20.4686 19.1314 19.8418 18.8 19.4C18.4686 18.9582 17.8418 18.8686 17.4 19.2C15.7781 20.4165 13.7929 21.0496 11.7662 20.997C9.73942 20.9443 7.78983 20.2088 6.23327 18.9098C4.67671 17.6107 3.60437 15.8241 3.18999 13.8395C2.77561 11.8549 3.04346 9.78847 3.95015 7.97509C4.85685 6.1617 6.34926 4.70758 8.18559 3.84831C10.0219 2.98904 12.0946 2.77496 14.0678 3.24077C16.041 3.70658 17.7991 4.82497 19.0573 6.41476C20.3155 8.00455 21 9.97258 21 12V13C21 13.5304 20.7893 14.0392 20.4142 14.4142C20.0391 14.7893 19.5304 15 19 15C18.4696 15 17.9609 14.7893 17.5858 14.4142C17.2107 14.0392 17 13.5304 17 13V8.00001C17 7.44772 16.5523 7.00001 16 7.00001C15.4478 7.00001 15.0002 7.44752 15 7.99964C14.1643 7.37195 13.1256 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17C13.4881 17 14.8243 16.3499 15.7403 15.3183C15.8687 15.4989 16.0128 15.6696 16.1716 15.8284C16.9217 16.5786 17.9391 17 19 17C20.0609 17 21.0783 16.5786 21.8284 15.8284C22.5786 15.0783 23 14.0609 23 13V12C23 9.52205 22.1633 7.11667 20.6255 5.17359C19.0878 3.23052 16.939 1.86359 14.5273 1.29427ZM15 11.9958V12.0042C14.9977 13.6591 13.6555 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6555 9 14.9977 10.3409 15 11.9958Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

AtSignIcon.displayName = "AtSignIcon"
