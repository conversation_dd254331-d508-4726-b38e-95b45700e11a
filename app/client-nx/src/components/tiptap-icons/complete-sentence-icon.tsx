import * as React from "react"

export const CompleteSentenceIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          d="M2 6C2 5.44772 2.44772 5 3 5H21C21.5523 5 22 5.44772 22 6C22 6.55228 21.5523 7 21 7H3C2.44772 7 2 6.55228 2 6Z"
          fill="currentColor"
        />
        <path
          d="M2 11C2 10.4477 2.44772 10 3 10H15C15.5523 10 16 10.4477 16 11C16 11.5523 15.5523 12 15 12H3C2.44772 12 2 11.5523 2 11Z"
          fill="currentColor"
        />
        <path
          d="M4 16C2.89543 16 2 16.8954 2 18C2 19.1046 2.89543 20 4 20C5.10457 20 6 19.1046 6 18C6 16.8954 5.10457 16 4 16Z"
          fill="currentColor"
        />
        <path
          d="M8 18C8 16.8954 8.89543 16 10 16C11.1046 16 12 16.8954 12 18C12 19.1046 11.1046 20 10 20C8.89543 20 8 19.1046 8 18Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.9492 13.6854C18.8136 13.2762 18.4311 13 18 13C17.5689 13 17.1864 13.2762 17.0508 13.6854L16.3711 15.7364C16.2717 16.0364 16.0364 16.2717 15.7364 16.3711L13.6854 17.0508C13.2762 17.1864 13 17.5689 13 18C13 18.4311 13.2762 18.8136 13.6854 18.9492L15.7364 19.6289C16.0364 19.7283 16.2717 19.9636 16.3711 20.2636L17.0508 22.3146C17.1864 22.7238 17.5689 23 18 23C18.4311 23 18.8136 22.7238 18.9492 22.3146L19.6289 20.2636C19.7283 19.9636 19.9636 19.7283 20.2636 19.6289L22.3146 18.9492C22.7238 18.8136 23 18.4311 23 18C23 17.5689 22.7238 17.1864 22.3146 17.0508L20.2636 16.3711C19.9636 16.2717 19.7283 16.0364 19.6289 15.7364L18.9492 13.6854ZM16.9559 18C17.3854 17.7445 17.7445 17.3854 18 16.9559C18.2555 17.3854 18.6146 17.7445 19.0441 18C18.6146 18.2555 18.2555 18.6146 18 19.0441C17.7445 18.6146 17.3854 18.2555 16.9559 18Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

CompleteSentenceIcon.displayName = "CompleteSentenceIcon"
