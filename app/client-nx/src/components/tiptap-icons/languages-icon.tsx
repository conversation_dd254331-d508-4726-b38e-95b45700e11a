import * as React from "react"

export const LanguagesIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          d="M7 1C6.44772 1 6 1.44772 6 2C6 2.55228 6.44772 3 7 3H8C8.55228 3 9 2.55228 9 2C9 1.44772 8.55228 1 8 1H7Z"
          fill="currentColor"
        />
        <path
          d="M9.22288 7.3629L10.1315 6H2C1.44772 6 1 5.55228 1 5C1 4.44772 1.44772 4 2 4H11.9853C11.9957 3.99984 12.0061 3.99984 12.0166 4H14C14.5523 4 15 4.44772 15 5C15 5.55228 14.5523 6 14 6H12.5352L10.8321 8.55468C10.7955 8.60948 10.7537 8.66053 10.7071 8.70709L8.91421 10.5L11.7071 13.2929C12.0976 13.6834 12.0976 14.3166 11.7071 14.7071C11.3166 15.0976 10.6834 15.0976 10.2929 14.7071L7.49999 11.9142L4.70711 14.7071C4.31658 15.0976 3.68342 15.0976 3.29289 14.7071C2.90237 14.3166 2.90237 13.6834 3.29289 13.2929L6.08578 10.5L4.29289 8.70711C3.90237 8.31658 3.90237 7.68342 4.29289 7.29289C4.68342 6.90237 5.31658 6.90237 5.70711 7.29289L7.49999 9.08578L9.22288 7.3629Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17 11C17.3787 11 17.725 11.214 17.8944 11.5528L20.8849 17.5338C20.8918 17.5468 20.8983 17.5599 20.9046 17.5731L22.8944 21.5528C23.1414 22.0468 22.9412 22.6474 22.4472 22.8944C21.9532 23.1414 21.3525 22.9412 21.1055 22.4472L19.3819 19H14.618L12.8944 22.4472C12.6474 22.9412 12.0467 23.1414 11.5528 22.8944C11.0588 22.6474 10.8586 22.0468 11.1055 21.5528L13.0964 17.5712C13.102 17.5592 13.1079 17.5474 13.1141 17.5358L16.1055 11.5528C16.2749 11.214 16.6212 11 17 11ZM15.618 17H18.3819L17 14.2361L15.618 17Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

LanguagesIcon.displayName = "LanguagesIcon"
