@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

body {
  --tt-toolbar-height: 44px;
  --tt-theme-text: var(--tt-gray-light-900);

  .dark & {
    --tt-theme-text: var(--tt-gray-dark-900);
  }
}

body {
  font-family: "Inter", sans-serif;
  color: var(--tt-theme-text);
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  padding: 0;
}

html,
body,
#root,
#app {
  height: 100%;
  background-color: var(--tt-bg-color);
}

::-webkit-scrollbar {
  width: 0.25rem;
}

* {
  scrollbar-width: thin;
  scrollbar-color: var(--tt-scrollbar-color) transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--tt-scrollbar-color);
  border-radius: 9999px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

.tiptap.ProseMirror {
  font-family: "DM Sans", sans-serif;
}

.notion-like-editor-wrapper {
  display: flex;
  flex-direction: column;
}

.notion-like-editor-content {
  max-width: 768px;
  width: 100%;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.notion-like-editor-content .tiptap.ProseMirror.notion-like-editor {
  flex: 1;
  padding: 3rem 3rem;
}

@media screen and (max-width: 768px) {
  .notion-like-editor-content .tiptap.ProseMirror.notion-like-editor {
    padding: 1.5rem;
    padding-bottom: 4rem;
  }
}

// Spinner
.spinner-container {
  width: 100%;
  height: 100%;
  flex-grow: 1;
  flex-shrink: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .spinner-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    svg {
      animation: spin 1s linear infinite;
      height: 1.25rem;
      width: 1.25rem;

      circle {
        opacity: 0.25;
        stroke: currentColor;
        stroke-width: 4;
      }

      path {
        opacity: 0.75;
        fill: currentColor;
      }
    }

    .spinner-loading-text {
      text-align: center;
    }
  }
}
