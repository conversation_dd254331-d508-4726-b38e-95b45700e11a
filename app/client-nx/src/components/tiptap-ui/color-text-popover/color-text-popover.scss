:root {
  --tiptap-ouline-fallback-color: var(--tt-gray-light-a-200);
}

.dark {
  --tiptap-ouline-fallback-color: var(--tt-gray-dark-a-200);
}

.tiptap-button-color-text-popover {
  position: relative;
  width: 1.25rem;
  height: 1.25rem;
  margin: 0 -0.175rem;
  border-radius: var(--tt-radius-xl);
  background-color: var(--active-highlight-color);
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: inherit;
    box-sizing: border-box;
    border: 1px solid
      var(--active-highlight-color, var(--tiptap-ouline-fallback-color));
    filter: brightness(95%);
    mix-blend-mode: multiply;

    .dark & {
      filter: brightness(140%);
      mix-blend-mode: lighten;
    }
  }
}
