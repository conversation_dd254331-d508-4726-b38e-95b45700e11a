:root {
  --emoji-input-bg-color: var(--white);
  --emoji-input-border-color: var(--tt-gray-light-a-100);
  --emoji-input-focus: var(--tt-brand-color-500);
}

.dark {
  --emoji-input-border-color: var(--tt-gray-dark-a-50);
  --emoji-input-bg-color: var(--tt-gray-dark-50);
  --emoji-input-focus: var(--tt-brand-color-500);
}

.tiptap-input.emoji-menu-search-input {
  border: 1px solid var(--emoji-input-border-color);
  border-radius: var(--tt-radius-lg);
  background-color: var(--emoji-input-bg-color);

  &:focus {
    border-color: var(--emoji-input-focus);
  }
}

.emoji-menu-list {
  max-height: 14.063rem;
  overflow-y: scroll;
  overscroll-behavior: contain;
  width: 100%;
}
