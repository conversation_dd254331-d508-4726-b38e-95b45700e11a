:root {
  --tiptap-ai-prompt-focused-border-color: var(--tt-brand-color-500);
  --tiptap-ai-prompt-border-color: var(--tt-gray-light-a-200);
  --tiptap-ai-loading-background-color: var(--tt-gray-light-a-200);
}

.dark {
  --tiptap-ai-prompt-focused-border-color: var(--tt-brand-color-500);
  --tiptap-ai-prompt-border-color: var(--tt-gray-dark-a-200);
  --tiptap-ai-loading-background-color: var(--tt-gray-dark-a-200);
}

.tiptap-ai-menu-progress {
  display: flex;
  align-items: center;
  padding: 6px;
  justify-content: space-between;
  width: 100%;
}

.tiptap-spinner-alt {
  color: var(--tt-brand-color-500);
  font-size: 12px;
  font-weight: 600;
  line-height: normal;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dots-container {
  display: flex;
  gap: 5px;
  align-items: center;
}

.dot {
  border-radius: 50%;
  background: var(--tiptap-ai-loading-background-color);
}

.dot:nth-child(1) {
  width: 6px;
  height: 6px;
  animation: loading1 0.8s infinite ease-in-out;
  animation-delay: 0s;
}

.dot:nth-child(2) {
  width: 12px;
  height: 12px;
  animation: loading2 0.8s infinite ease-in-out;
  animation-delay: 0.1s;
}

.dot:nth-child(3) {
  width: 8px;
  height: 8px;
  animation: loading3 0.8s infinite ease-in-out;
  animation-delay: 0.2s;
}

@keyframes loading1 {
  0%,
  80%,
  100% {
    background: var(--tiptap-ai-loading-background-color);
    transform: scale(0.8);
  }
  40% {
    background: var(--tt-brand-color-400);
    transform: scale(1.2);
  }
}

@keyframes loading2 {
  0%,
  80%,
  100% {
    background: var(--tiptap-ai-loading-background-color);
    transform: scale(0.8);
  }
  40% {
    background: var(--tt-brand-color-500);
    transform: scale(1.2);
  }
}

@keyframes loading3 {
  0%,
  80%,
  100% {
    background: var(--tiptap-ai-loading-background-color);
    transform: scale(0.8);
  }
  40% {
    background: var(--tt-brand-color-200);
    transform: scale(1.2);
  }
}
