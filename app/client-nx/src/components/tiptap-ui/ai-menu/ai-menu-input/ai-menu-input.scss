:root {
  --tiptap-ai-prompt-input-bg-color: var(--white);
  --tiptap-ai-prompt-input-focused-border-color: var(--tt-brand-color-500);
  --tiptap-ai-prompt-input-border-color: var(--tt-gray-light-a-200);

  [data-active-state="off"] {
    --tiptap-ai-prompt-input-text-color: var(--tt-gray-light-a-400);

    &.tiptap-ai-prompt-input:hover {
      --tiptap-ai-prompt-input-border-color: var(--tt-gray-light-a-300);
    }
  }
}

.dark {
  --tiptap-ai-prompt-input-bg-color: var(--tt-gray-dark-50);
  --tiptap-ai-prompt-input-focused-border-color: var(--tt-brand-color-500);
  --tiptap-ai-prompt-input-border-color: var(--tt-gray-dark-a-200);

  [data-active-state="off"] {
    --tiptap-ai-prompt-input-text-color: var(--tt-gray-dark-a-400);

    &.tiptap-ai-prompt-input:hover {
      --tiptap-ai-prompt-input-border-color: var(--tt-gray-dark-a-300);
    }
  }
}

.tiptap-ai-prompt-input {
  background-color: var(--tiptap-ai-prompt-input-bg-color);
  border: 1px solid var(--tiptap-ai-prompt-input-border-color);
  border-radius: var(--tt-radius-lg, 0.75rem);
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  padding: 0.375rem;
  width: 100%;

  &[data-focused="true"] {
    border-color: var(--tiptap-ai-prompt-input-focused-border-color);
  }
}

.tiptap-ai-prompt-input-content {
  max-height: 9rem;
  overflow-y: auto;
  overscroll-behavior: contain;
  resize: none;

  .tiptap.ProseMirror {
    font-family: "Inter", sans-serif;
  }
}

[data-active-state="off"] {
  &.tiptap-ai-prompt-input {
    height: 2.75rem;
    cursor: pointer;
    border: 1px solid var(--tiptap-ai-prompt-input-border-color);
    color: var(--tiptap-ai-prompt-input-text-color);

    &:hover {
      border: 1px solid var(--tiptap-ai-prompt-input-border-color);
    }
  }

  .tiptap-ai-prompt-input-toolbar {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }
}

.tiptap-ai-prompt-input-placeholder {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tiptap-ai-prompt-input-placeholder-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
}

.tiptap-ai-prompt-input-placeholder-icon {
  width: 1rem;
  height: 1rem;
}

.tiptap-ai-prompt-input-placeholder-text {
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
}
