:root {
  --tiptap-avatar-border-color: var(--white);
  --tiptap-avatar-fallback-bg-color: var(--tt-gray-light-200);
  --tiptap-avatar-item-bg-color: var(--tt-gray-light-200);
  --tiptap-avatar-fallback-text-color: var(--tt-gray-light-a-600);
}

.dark {
  --tiptap-avatar-border-color: var(--tt-gray-dark-200);
  --tiptap-avatar-fallback-bg-color: var(--tt-gray-dark-200);
  --tiptap-avatar-item-bg-color: var(--tt-gray-dark-300);
  --tiptap-avatar-fallback-text-color: var(--tt-gray-dark-a-600);
}

.tiptap-avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  overflow: hidden;
  user-select: none;
  flex-shrink: 0;

  &[data-size="default"] {
    width: 1.5rem;
    height: 1.5rem;

    .tiptap-avatar-fallback {
      font-size: 0.5rem;
    }
  }

  &[data-size="sm"] {
    width: 1.25rem;
    height: 1.25rem;

    .tiptap-avatar-fallback {
      font-size: 0.4375rem;
    }
  }

  &[data-size="lg"] {
    width: 1.75rem;
    height: 1.75rem;

    .tiptap-avatar-fallback {
      font-size: 0.625rem;
    }
  }

  &[data-size="xl"] {
    width: 2.25rem;
    height: 2.25rem;

    .tiptap-avatar-fallback {
      font-size: 0.75rem;
    }
  }
}

.tiptap-avatar-item {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  background-color: var(
    --dynamic-user-color,
    var(--tiptap-avatar-item-bg-color)
  );
  border-radius: 50%;
}

.tiptap-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.tiptap-avatar-fallback {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--tiptap-avatar-fallback-text-color);
  border-radius: 50%;
  font-weight: 600;
}

.tiptap-avatar-bg {
  position: absolute;
  inset: 0;
  background-color: var(
    --dynamic-user-color,
    var(--tiptap-avatar-item-bg-color)
  );
  border-radius: 50%;
}

.tiptap-avatar-group {
  display: inline-flex;
  align-items: center;

  .tiptap-avatar-image,
  .tiptap-avatar-fallback {
    border: 2px solid var(--tiptap-avatar-border-color);
  }

  .tiptap-avatar:not(:first-child) {
    margin-left: -0.5rem;
  }
}
