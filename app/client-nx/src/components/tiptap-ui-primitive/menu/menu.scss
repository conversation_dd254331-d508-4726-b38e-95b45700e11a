.tiptap-menu-content {
  z-index: 50;
  display: flex;
  flex-direction: column;
  height: 100%;
  outline: none;
  min-width: var(--popover-anchor-width);

  &[data-state="closed"] {
    display: none;
  }

  &[data-state="open"] {
    animation: popover 150ms ease-out;
  }
}

.tiptap-menu-group {
  display: none;

  &:has([role="menuitem"]),
  &:has([role="option"]) {
    display: block;
  }
}

.tiptap-menu-item {
  width: 100%;
}

@keyframes popover {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-2px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes zoom {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
