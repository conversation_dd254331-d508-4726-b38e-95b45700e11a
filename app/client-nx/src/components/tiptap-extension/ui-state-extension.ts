import { Extension } from "@tiptap/core"

interface UiState {
  /**
   * Indicates if AI generation is currently in selection mode
   * If its in selection mode, it means text generated by AI will be automatically accepted
   */
  aiGenerationIsSelection: boolean
  aiGenerationIsLoading: boolean
  aiGenerationActive: boolean
  aiGenerationHasMessage: boolean
  commentInputVisible: boolean
  lockDragHandle: boolean
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    uiState: {
      aiGenerationSetIsSelection: (value: boolean) => ReturnType
      aiGenerationSetIsLoading: (value: boolean) => ReturnType
      aiGenerationShow: () => ReturnType
      aiGenerationHide: () => ReturnType
      aiGenerationHasMessage: (value: boolean) => ReturnType

      commentInputShow: () => ReturnType
      commentInputHide: () => ReturnType

      setLockDragHandle: (value: boolean) => ReturnType

      resetUiState: () => ReturnType
    }
  }

  interface Storage {
    uiState: UiState
  }
}

export const UiState = Extension.create<UiState>({
  name: "uiState",

  addStorage() {
    return {
      aiGenerationIsSelection: false,
      aiGenerationIsLoading: false,
      aiGenerationActive: false,
      aiGenerationHasMessage: false,
      commentInputVisible: false,
      lockDragHandle: false,
    }
  },

  addCommands() {
    return {
      aiGenerationSetIsSelection: (value: boolean) => () => {
        this.storage.aiGenerationIsSelection = value
        return true
      },

      aiGenerationSetIsLoading: (value: boolean) => () => {
        this.storage.aiGenerationIsLoading = value
        return true
      },

      setLockDragHandle: (value: boolean) => () => {
        this.storage.lockDragHandle = value
        return true
      },

      aiGenerationShow: () => () => {
        this.storage.aiGenerationActive = true
        return true
      },

      aiGenerationHide: () => () => {
        this.storage.aiGenerationActive = false
        return true
      },

      aiGenerationHasMessage: (value: boolean) => () => {
        this.storage.aiGenerationHasMessage = value
        return true
      },

      commentInputShow: () => () => {
        this.storage.commentInputVisible = true
        return true
      },

      commentInputHide: () => () => {
        this.storage.commentInputVisible = false
        return true
      },

      resetUiState: () => () => {
        this.storage.aiGenerationIsSelection = false
        this.storage.aiGenerationIsLoading = false
        this.storage.aiGenerationActive = false
        this.storage.aiGenerationHasMessage = false
        this.storage.commentInputVisible = false
        this.storage.lockDragHandle = false
        return true
      },
    }
  },

  onCreate() {
    this.storage = {
      aiGenerationIsSelection: false,
      aiGenerationIsLoading: false,
      aiGenerationActive: false,
      aiGenerationHasMessage: false,
      commentInputVisible: false,
      lockDragHandle: false,
    }
  },
})
