{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "start:test": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@floating-ui/react": "^0.27.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@tiptap-cloud/provider": "^3.0.0", "@tiptap-pro/extension-ai": "^3.0.1", "@tiptap-pro/extension-comments": "^3.0.1", "@tiptap/core": "^3.0.7", "@tiptap/extension-collaboration": "^3.0.7", "@tiptap/extension-collaboration-caret": "^3.0.7", "@tiptap/extension-color": "^3.0.7", "@tiptap/extension-drag-handle-react": "^3.0.7", "@tiptap/extension-emoji": "^3.0.7", "@tiptap/extension-highlight": "^3.0.7", "@tiptap/extension-horizontal-rule": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-list": "^3.0.7", "@tiptap/extension-mathematics": "^3.0.7", "@tiptap/extension-mention": "^3.0.7", "@tiptap/extension-subscript": "^3.0.7", "@tiptap/extension-superscript": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/extension-typography": "^3.0.7", "@tiptap/extension-unique-id": "^3.0.7", "@tiptap/extensions": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@tiptap/suggestion": "^3.0.7", "is-hotkey": "^0.2.0", "next": "latest", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hotkeys-hook": "^5.1.0", "react-textarea-autosize": "^8.5.9", "yjs": "^13.6.27"}, "devDependencies": {"@ariakit/react": "^0.4.17", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/is-hotkey": "^0.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "next-rspack": "canary", "sass": "^1.89.2", "tailwindcss": "^4", "typescript": "^5"}}