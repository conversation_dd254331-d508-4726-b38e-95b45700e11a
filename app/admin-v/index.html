<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <link rel="icon" href="/favicon.ico" />
    <title>Rovella后台管理系统</title>
    <!--[if lt IE 11]>
      <script>
        window.location.href = '/html/ie.html'
      </script>
    <![endif]-->
    <style>
      #app {
        min-height: 100%;
        background: white;
      }

      #loader {
        position: fixed;
        top: 35%;
        left: 50%;
        display: grid;
        grid-template-columns: repeat(3, 50px);
        grid-template-rows: repeat(3, 50px);
        gap: 5px;
        margin-left: -80px;
        transform: rotate(45deg);
      }

      #loader span {
        background-color: #fff;
        border-radius: 8px;
        animation: loading 1.5s ease-in-out infinite;
      }

      @keyframes loading {
        0%,
        100% {
          opacity: 0;
          transform: scale(0);
        }

        35%,
        65% {
          opacity: 1;
          transform: scale(1);
        }
      }

      /* 1~3 */
      #loader span:nth-child(-n + 3) {
        background-color: #E4E7ED;
      }

      /* 4~6 */
      #loader span:nth-child(n + 4):nth-child(-n + 6) {
        background-color: #909399;
      }

      /* 7~9 */
      #loader span:nth-child(n + 7):nth-child(-n + 9) {
        background-color: #606266;
      }

      .load_title {
        font-family: 'Open Sans';
        color: #909399;
        font-size: 19px;
        width: 100%;
        text-align: center;
        z-index: 99999;
        position: fixed;
        top: 65%;
        left: 0;
        opacity: 1;
        line-height: 30px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <div id="loader">
        <span></span>
        <span></span>
        <span></span>

        <span></span>
        <span></span>
        <span></span>

        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="load_title">正在加载系统资源，请耐心等待</div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
